{"train_micro_batch_size_per_gpu": 1, "gradient_accumulation_steps": 1, "steps_per_print": 100, "gradient_clipping": 5, "fp16": {"enabled": false, "auto_cast": false, "loss_scale": 0, "initial_scale_power": 16, "loss_scale_window": 256, "hysteresis": 2, "consecutive_hysteresis": false, "min_loss_scale": 1}, "bf16": {"enabled": false}, "zero_force_ds_cpu_optimizer": false, "zero_optimization": {"stage": 2, "offload_optimizer": {"device": "none", "pin_memory": true}, "allgather_partitions": true, "allgather_bucket_size": 500000000.0, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000.0, "contiguous_gradients": true}, "optimizer": {"type": "AdamW", "params": {"lr": 0.001, "weight_decay": 0.0001, "torch_adam": true, "adam_w_mode": true}}}