--extra-index-url https://download.pytorch.org/whl/cu121
--extra-index-url https://aiinfra.pkgs.visualstudio.com/PublicPackages/_packaging/onnxruntime-cuda-12/pypi/simple/ # https://github.com/microsoft/onnxruntime/issues/21684
conformer==0.3.2
deepspeed==0.15.1; sys_platform == 'linux'
diffusers==0.29.0
fastapi==0.116.1
fastapi-cli==0.0.8
# gdown==5.1.0
# gradio==5.4.0
# grpcio==1.57.0
# grpcio-tools==1.57.0
hydra-core==1.3.2
HyperPyYAML==1.2.2
inflect==7.3.1
librosa==0.10.2
lightning==2.2.4
matplotlib==3.7.5
modelscope==1.20.0
networkx==3.1
omegaconf==2.3.0
onnx==1.17.0
onnxruntime-gpu==1.22.0
openai-whisper==20240930
protobuf==4.25
pyarrow==18.1.0
pydantic==2.7.0
pyworld==0.3.4
rich==13.7.1
soundfile==0.12.1
tensorboard==2.14.0
tensorrt-cu12==**********
tensorrt-cu12-bindings==**********
tensorrt-cu12-libs==**********
transformers>=4.51.1
uvicorn==0.30.0
# WeTextProcessing==1.0.3
wget==3.2

numpy==1.26.0
gdown==5.2.0

pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu128