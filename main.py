import os
import sys
import logging
import traceback

from datetime import datetime
from typing import Optional, Literal, Type, AsyncGenerator, Any
from starlette.concurrency import run_in_threadpool

from utils import _tensor_to_bytes, download_audio_file, AudioProcessingError, get_media_type, cleanup_temp_file

import uvicorn
from fastapi import FastAPI, APIRouter, HTTPException, Response
from fastapi.responses import StreamingResponse
from pydantic import BaseModel, field_validator

# 设置日志
logging.basicConfig(
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

# 初始化 FastAPI
app = FastAPI()

# 初始化模型
sys.path.append('third_party/Matcha-TTS')
import torch
from vllm import ModelRegistry
from cosyvoice.vllm.cosyvoice2 import CosyVoice2ForCausalLM
from cosyvoice.cli.cosyvoice import CosyVoice2
from cosyvoice.utils.file_utils import load_wav
import asyncio

ModelRegistry.register_model("CosyVoice2ForCausalLM", CosyVoice2ForCausalLM)

model = CosyVoice2(
    'pretrained_models/CosyVoice2-0.5B',
    load_jit=True,
    load_trt=True,
    load_vllm=True,
    fp16=True,
    trt_concurrent=10
)


class TTSRequest(BaseModel):
    text: str
    mode: str = "zero_shot"
    prompt_audio_path: str = "./asset/zero_shot_prompt.wav"
    prompt_text: str = "希望你以后能够做的比我还好呦。"
    instruct_text: Optional[str] = ""
    speed: float = 1.0
    stream: bool = False
    output_format: str = "wav"  # pcm, wav, mp3
    zero_shot_spk_id: str = ''
    
    # 音频编码相关参数
    bit_rate: Optional[int] = 192000        # 比特率，默认 192kbps
    compression_level: Optional[int] = 2     # 压缩级别 (0-9)
    

    @field_validator('speed')
    @classmethod
    def check_speed(cls, v):
        if v <= 0:
            raise ValueError('Speed must be positive')
        return v

    @field_validator('mode')
    @classmethod
    def check_mode(cls, v):
        if v not in ["zero_shot", "cross_lingual", "instruct2"]:
            raise ValueError('Invalid mode')
        return v
    
    @field_validator('output_format')
    @classmethod
    def check_format(cls, v):
        if v not in ["pcm", "wav", "mp3"]:
            raise ValueError('Unsupported audio format')
        return v

    @field_validator('bit_rate')
    @classmethod
    def check_bit_rate(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Bit rate must be positive')
        return v
    
    @field_validator('compression_level')
    @classmethod
    def check_compression_level(cls, v):
        if v is not None and (v < 0 or v > 9):
            raise ValueError('Compression level must be between 0 and 9')
        return v

async def convert_audio_tensor_to_bytes(
        audio_data_generator: AsyncGenerator[dict, None],
        format: str = None) -> AsyncGenerator[bytes, Any]:
    tensor: torch.Tensor | None = None
    async for chunk in audio_data_generator:
        if tensor is not None:
            tensor = torch.concat([tensor, chunk], dim=1)
        else:
            tensor = chunk

    yield await run_in_threadpool(_tensor_to_bytes, tensor, format, 16000)

async def generator_wrapper(audio_data_generator: AsyncGenerator[dict, None]) -> AsyncGenerator[torch.Tensor, None]:
    async for chunk in audio_data_generator:
        yield chunk["tts_speech"]

async def generate_audio(request: TTSRequest):
    try:
        prompt_speech_16k = load_wav(request.prompt_audio_path, 16000)
    
        audio_tensor_data_generator = generator_wrapper(model.inference_zero_shot(
            request.text, 
            request.prompt_text, 
            prompt_speech_16k, 
            stream=request.stream,
            speed=request.speed,
            zero_shot_spk_id=request.zero_shot_spk_id
        ))
        # 收集所有音频数据
        audio_bytes_data_generator = convert_audio_tensor_to_bytes(
            audio_tensor_data_generator,
            request.output_format,
        )
        return audio_bytes_data_generator
    except Exception as e:
        logger.error(f"Error generating audio: {e}")


@app.post("/tts")
async def text_to_speech(request: TTSRequest):
    try:
        media_type = get_media_type(request.output_format)
        
        headers = {
            "Content-Disposition": f"attachment; filename=output.{request.output_format}",
            "X-Processing-Time": str(datetime.now().timestamp())
        }
        return StreamingResponse(content=await generate_audio(request), media_type=media_type, headers=headers)
    except AudioProcessingError as e:
        logger.error(f"Error in text_to_speech: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail={"error": str(e), "timestamp": datetime.now().isoformat()}
        )
            

if __name__ == "__main__":
    # gunicorn -w 2 -k uvicorn.workers.UvicornWorker -b 0.0.0.0:8000 --timeout 120 app:app
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info",
        timeout_keep_alive=60,
        limit_concurrency=100
    )